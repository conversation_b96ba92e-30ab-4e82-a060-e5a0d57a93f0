// This file was generated by Firebase Studio.
'use server';
/**
 * @fileOverview Sentiment analysis of survey responses.
 *
 * - analyzeSentiment - Analyzes the sentiment of survey responses.
 * - SentimentAnalysisInput - Input type for analyzeSentiment function.
 * - SentimentAnalysisOutput - Return type for analyzeSentiment function.
 */

import {ai} from '@/ai/ai-instance';
import {z} from 'genkit';

const SentimentAnalysisInputSchema = z.object({
  verbatimResponse: z.string().describe('The open-ended survey response to analyze.'),
  context: z.string().optional().describe('Optional context about the survey or respondent.'),
  language: z.string().optional().describe('Language of the survey response. Defaults to English (en).'),
});
export type SentimentAnalysisInput = z.infer<typeof SentimentAnalysisInputSchema>;

const SentimentAnalysisOutputSchema = z.object({
  sentimentScore: z.number().describe('A score between -1 and 1 indicating the sentiment of the response.'),
  sentimentLabel: z.enum(['Positive', 'Negative', 'Neutral']).describe('The overall sentiment of the response.'),
  reason: z.string().optional().describe('Reasoning behind the sentiment score.'),
});
export type SentimentAnalysisOutput = z.infer<typeof SentimentAnalysisOutputSchema>;

export async function analyzeSentiment(input: SentimentAnalysisInput): Promise<SentimentAnalysisOutput> {
  return sentimentAnalysisFlow(input);
}

const sentimentAnalysisPrompt = ai.definePrompt({
  name: 'sentimentAnalysisPrompt',
  input: {
    schema: z.object({
      verbatimResponse: z.string().describe('The open-ended survey response to analyze.'),
      context: z.string().optional().describe('Optional context about the survey or respondent.'),
      language: z.string().optional().describe('Language of the survey response. Defaults to English (en).'),
    }),
  },
  output: {
    schema: z.object({
      sentimentScore: z.number().describe('A score between -1 and 1 indicating the sentiment of the response.'),
      sentimentLabel: z.enum(['Positive', 'Negative', 'Neutral']).describe('The overall sentiment of the response.'),
      reason: z.string().optional().describe('Reasoning behind the sentiment score.'),
    }),
  },
  prompt: `Analyze the sentiment of the following survey response. Provide a sentiment score between -1 and 1, a sentiment label (Positive, Negative, or Neutral), and the reasoning behind the score.

Survey Response: {{{verbatimResponse}}}

{{#if context}}
Context: {{{context}}}
{{/if}}

{{#if language}}
Note: The survey response is in {{language}} language. Please analyze sentiment considering {{language}} language patterns and expressions. Cultural context may affect how sentiment is expressed.
{{/if}}

Output in JSON format.`,
});

const sentimentAnalysisFlow = ai.defineFlow<
  typeof SentimentAnalysisInputSchema,
  typeof SentimentAnalysisOutputSchema
>({
  name: 'sentimentAnalysisFlow',
  inputSchema: SentimentAnalysisInputSchema,
  outputSchema: SentimentAnalysisOutputSchema,
}, async input => {
  const {output} = await sentimentAnalysisPrompt(input);
  return output!;
});
