# Firebase Studio

This is a NextJS starter in Firebase Studio.

## Key Functionalities

### AI Mode Selection
- Flexible switching between Local AI (Ollama) and Cloud AI (Google Gemini)
- Real-time status monitoring of AI services
- Easy-to-use interface with mode toggle switch

### AI Model Configuration
- **Local AI Support**:
  - Multiple model options: Gemma 3, Qwen 2.5 Coder, DeepSeek R1 14B, Mistral
  - Automatic model availability detection
  - Built-in installation guidance
- **Cloud AI Integration**:
  - Google Gemini integration
  - Secure API key configuration

### Survey Data Analysis
- Data Upload: Support for CSV and Excel file formats
- KPI Detection: Correlation analysis of numerical responses
- Thematic Analysis: AI-powered categorization of open-ended responses
- Sentiment Analysis: Evaluation of response sentiment and satisfaction levels
- Dashboard Visualization: Clear presentation of analysis results

### Development Tools
- Built-in AI testing interface
- Real-time model status monitoring
- Interactive response testing capabilities

To get started, take a look at src/app/page.tsx.
