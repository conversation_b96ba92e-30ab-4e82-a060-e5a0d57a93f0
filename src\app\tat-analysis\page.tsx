"use client"

import { useState, useEffect } from 'react';
// Import the full xlsx library for file processing
import * as XLSX from 'xlsx';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { CalendarIcon, Download } from "lucide-react";
import { cn } from "@/lib/utils";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { sampleTATData } from '@/utils/sample-tat-data';
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { DateRange } from "react-day-picker";

// Define the TAT report data structure
interface TATReportItem {
  serialNumber: string;
  affiliate: string;
  workItemNo: string;
  branchName: string;
  currentWorkStep: string;
  createdBy: string;
  workitemCreationDate: Date | null;
  accountNo: string;
  accountNoGenerationDate: Date | null;
  zones: string;
  businessUnit: string;
  riskRating: string;
  accountAuthorizationDate: Date | null;
  accountNoGenerationTAT: number | null;
  csrSubmissionTAT: number | null;
  csrName: string;
  cpcMakerDuration: number | null;
  cpcMaker: string;
  cpcSupervisorDuration: number | null;
  cpcSupervisor: string;
  exceptionResolutionTAT: number | null;
  cpcExceptionName: string;
  complianceDuration: number | null;
  complianceName: string;
  totalTurnaroundTime: number | null;
  rpcEntryDate: Date | null;
  rpcExitDate: Date | null;
  durationProcessTimeFormatted: string;
  rawDurationProcessTime: number | null;
  durationProcessTime: number; // 1, 2, or 3 days
}

// Colors for duration categories
const DURATION_COLORS = {
  '1 day': 'bg-green-100 text-green-800',
  '2 days': 'bg-yellow-100 text-yellow-800',
  '3+ days': 'bg-red-100 text-red-800'
};

export default function TATAnalysisPage() {
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // TAT data state
  const [tatData, setTatData] = useState<TATReportItem[]>([]);
  const [filteredData, setFilteredData] = useState<TATReportItem[]>([]);

  // Filters
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [selectedAgent, setSelectedAgent] = useState<string>("all");
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [agents, setAgents] = useState<string[]>([]);
  const [showAgentSelector, setShowAgentSelector] = useState<boolean>(false);

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  // Process the Excel or CSV file
  const handleUpload = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select an Excel or CSV file to upload.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    const startTime = Date.now();

    try {
      // Detect file type
      const fileType = file.name.split('.').pop()?.toLowerCase();
      let jsonData: any[] = [];

      if (fileType === 'csv') {
        // Read CSV as text
        const text = await file.text();
        // Parse CSV using XLSX
        const workbook = XLSX.read(text, { type: 'string' });
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        jsonData = XLSX.utils.sheet_to_json<any>(worksheet);
      } else {
        // Default: Excel
        const data = await file.arrayBuffer();
        const workbook = XLSX.read(data);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        jsonData = XLSX.utils.sheet_to_json<any>(worksheet);
      }

      // Process the data
      const processedData = processExcelData(jsonData);
      setTatData(processedData);
      setFilteredData(processedData);

      // Extract unique agents
      const uniqueAgents = Array.from(new Set(processedData.map(item => item.createdBy)))
        .filter(agent => agent) // Remove empty values
        .sort();
      setAgents(uniqueAgents);
      setSelectedAgents(uniqueAgents); // Initialize with all agents selected

      // Find min and max dates
      const dates = processedData
        .map(item => item.workitemCreationDate)
        .filter(date => date !== null) as Date[];

      if (dates.length > 0) {
        const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
        const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));

        // Set date range to min and max dates
        setDateRange({
          from: minDate,
          to: maxDate
        });
      }

      // Calculate processing time
      const endTime = Date.now();
      const totalTime = (endTime - startTime) / 1000; // Convert to seconds

      toast({
        title: "Upload successful",
        description: `Processed ${processedData.length} records in ${totalTime.toFixed(1)} seconds.`
      });
    } catch (error: any) {
      console.error("Error processing file:", error);
      toast({
        title: "Upload failed",
        description: `Error processing file: ${error.message}`,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Format duration in JJ:HH:MM:SS format
  const formatDuration = (minutes: number): string => {
    if (!minutes && minutes !== 0) return '';

    const days = Math.floor(minutes / (24 * 60));
    const hours = Math.floor((minutes % (24 * 60)) / 60);
    const mins = Math.floor(minutes % 60);
    const secs = Math.floor((minutes * 60) % 60);

    return `${days.toString().padStart(2, '0')}:${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Process the Excel/CSV data
  const processExcelData = (data: any[]): TATReportItem[] => {
    return data.map(row => {
      // Parse dates
      const workitemCreationDate = parseExcelDate(row['Workitem_Creation_Date']);
      const rpcExitDate = parseExcelDate(row['RPC Exit Date']);

      // Calculate raw duration if not provided
      let rawDuration = row['Raw duration process time'];
      if ((rawDuration === undefined || rawDuration === null) && workitemCreationDate && rpcExitDate) {
        // Calculate the difference in days
        rawDuration = (rpcExitDate.getTime() - workitemCreationDate.getTime()) / (24 * 60 * 60 * 1000);
      }

      // Calculate duration process time (1, 2, or 3 days)
      let durationProcessTime = 3; // Default to 3+ days
      if (rawDuration !== undefined && rawDuration !== null) {
        if (rawDuration < 1) {
          durationProcessTime = 1;
        } else if (rawDuration >= 1 && rawDuration < 2) {
          durationProcessTime = 2;
        }
      }

      // Calculate total turnaround time in minutes if not provided
      let totalTurnaroundTime = parseFloat(row['Total Turnaround Time(Min)']) || null;
      if ((totalTurnaroundTime === null) && workitemCreationDate && rpcExitDate) {
        totalTurnaroundTime = (rpcExitDate.getTime() - workitemCreationDate.getTime()) / (60 * 1000);
      }

      // Generate formatted duration if not provided
      let durationProcessTimeFormatted = row['Duration process time JJ:HH:MM:SS'] || '';
      if (!durationProcessTimeFormatted && totalTurnaroundTime !== null) {
        durationProcessTimeFormatted = formatDuration(totalTurnaroundTime);
      }

      // Log the first row to debug column mapping
      if (data.indexOf(row) === 0) {
        console.log("First row data:", row);
      }

      return {
        serialNumber: row['numéro de série'] || '',
        affiliate: row['AFFILIATE'] || '',
        workItemNo: row['WorkItem No.'] || '',
        branchName: row['Branch Name'] || '',
        currentWorkStep: row['Current WorkStep'] || '',
        createdBy: row['Created By'] || '',
        workitemCreationDate: workitemCreationDate,
        accountNo: row['ACCOUNT_NO'] || '',
        accountNoGenerationDate: parseExcelDate(row['Account No Generation Date']),
        zones: row['Zones'] || '',
        businessUnit: row['Business Unit'] || '',
        riskRating: row['Risk Rating'] || '',
        accountAuthorizationDate: parseExcelDate(row['Account_Authorization_Date']),
        accountNoGenerationTAT: parseFloat(row['Account No Generation TAT(Min)']) || null,
        csrSubmissionTAT: parseFloat(row['CSR Submission TAT(Min)']) || null,
        csrName: row['CSR Name'] || '',
        cpcMakerDuration: parseFloat(row['CPC Maker Duration(Min)']) || null,
        cpcMaker: row['CPC Maker'] || '',
        cpcSupervisorDuration: parseFloat(row['CPC Supervisor Duration (Min)']) || null,
        cpcSupervisor: row['CPC Supervisor'] || '',
        exceptionResolutionTAT: parseFloat(row['Exception Resolution TAT(Min)']) || null,
        cpcExceptionName: row['CPC Exception Name'] || '',
        complianceDuration: parseFloat(row['Compliance Duration (Min)']) || null,
        complianceName: row['Compliance Name'] || '',
        totalTurnaroundTime: totalTurnaroundTime,
        rpcEntryDate: parseExcelDate(row['RPC Entry Date']),
        rpcExitDate: rpcExitDate,
        durationProcessTimeFormatted: durationProcessTimeFormatted,
        rawDurationProcessTime: rawDuration,
        durationProcessTime: durationProcessTime
      };
    });
  };

  // Parse Excel date (could be string or number)
  const parseExcelDate = (excelDate: any): Date | null => {
    if (!excelDate) return null;

    if (typeof excelDate === 'string') {
      // Try to parse French/custom date format like "29/Avr/2025 07:59:50"
      if (excelDate.includes('/')) {
        const frenchMonths: Record<string, string> = {
          'Jan': '01', 'Fév': '02', 'Mar': '03', 'Avr': '04',
          'Mai': '05', 'Jui': '06', 'Jul': '07', 'Aoû': '08',
          'Sep': '09', 'Oct': '10', 'Nov': '11', 'Déc': '12'
        };

        try {
          // Split date and time
          const [datePart, timePart] = excelDate.split(' ');

          // Parse date part (DD/MMM/YYYY)
          const [day, monthStr, year] = datePart.split('/');

          // Convert month name to number
          const month = frenchMonths[monthStr] || monthStr;

          // Create standardized date string
          const standardDate = `${year}-${month}-${day.padStart(2, '0')}`;

          // Add time if available
          const dateTimeStr = timePart ? `${standardDate}T${timePart}` : standardDate;

          const date = new Date(dateTimeStr);
          return isNaN(date.getTime()) ? null : date;
        } catch (e) {
          console.error("Error parsing custom date format:", excelDate, e);
          // Fall back to standard parsing
          const date = new Date(excelDate);
          return isNaN(date.getTime()) ? null : date;
        }
      } else {
        // Try to parse as ISO date string
        const date = new Date(excelDate);
        return isNaN(date.getTime()) ? null : date;
      }
    } else if (typeof excelDate === 'number') {
      // Excel stores dates as days since 1900-01-01
      const date = new Date(Math.round((excelDate - 25569) * 86400 * 1000));
      return isNaN(date.getTime()) ? null : date;
    }

    return null;
  };

  // Toggle agent selection
  const toggleAgentSelection = (agent: string) => {
    setSelectedAgents(prev => {
      if (prev.includes(agent)) {
        return prev.filter(a => a !== agent);
      } else {
        return [...prev, agent];
      }
    });
  };

  // Select all agents
  const selectAllAgents = () => {
    setSelectedAgents(agents);
  };

  // Deselect all agents
  const deselectAllAgents = () => {
    setSelectedAgents([]);
  };

  // Apply filters when dependencies change
  useEffect(() => {
    if (tatData.length === 0) return;

    let filtered = [...tatData];

    // Apply date range filter
    if (dateRange?.from) {
      const fromDate = dateRange.from;
      filtered = filtered.filter(item =>
        item.workitemCreationDate && item.workitemCreationDate >= fromDate
      );
    }

    if (dateRange?.to) {
      const toDate = dateRange.to;
      filtered = filtered.filter(item =>
        item.workitemCreationDate && item.workitemCreationDate <= toDate
      );
    }

    // Apply agent filter - either use dropdown or multi-select
    if (selectedAgent !== "all" && !showAgentSelector) {
      filtered = filtered.filter(item => item.createdBy === selectedAgent);
    } else if (showAgentSelector && selectedAgents.length > 0) {
      filtered = filtered.filter(item => selectedAgents.includes(item.createdBy));
    }

    setFilteredData(filtered);
  }, [tatData, dateRange, selectedAgent, selectedAgents, showAgentSelector]);

  // Export data to Excel with conditional formatting
  const exportToExcel = () => {
    if (filteredData.length === 0) {
      toast({
        title: "No data to export",
        description: "Please load data before exporting.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Prepare data for export
      const exportData = filteredData.map(item => ({
        'Serial Number': item.serialNumber,
        'Affiliate': item.affiliate,
        'WorkItem No.': item.workItemNo,
        'Branch Name': item.branchName,
        'Current WorkStep': item.currentWorkStep,
        'Created By': item.createdBy,
        'Workitem Creation Date': item.workitemCreationDate ? format(item.workitemCreationDate, 'dd/MMM/yyyy HH:mm:ss') : '',
        'Account No': item.accountNo,
        'Account No Generation Date': item.accountNoGenerationDate ? format(item.accountNoGenerationDate, 'dd/MMM/yyyy HH:mm:ss') : '',
        'Zones': item.zones,
        'Business Unit': item.businessUnit,
        'Risk Rating': item.riskRating,
        'Account Authorization Date': item.accountAuthorizationDate ? format(item.accountAuthorizationDate, 'dd/MMM/yyyy HH:mm:ss') : '',
        'Account No Generation TAT(Min)': item.accountNoGenerationTAT,
        'CSR Submission TAT(Min)': item.csrSubmissionTAT,
        'CSR Name': item.csrName,
        'CPC Maker Duration(Min)': item.cpcMakerDuration,
        'CPC Maker': item.cpcMaker,
        'CPC Supervisor Duration (Min)': item.cpcSupervisorDuration,
        'CPC Supervisor': item.cpcSupervisor,
        'Exception Resolution TAT(Min)': item.exceptionResolutionTAT,
        'CPC Exception Name': item.cpcExceptionName,
        'Compliance Duration (Min)': item.complianceDuration,
        'Compliance Name': item.complianceName,
        'Total Turnaround Time(Min)': item.totalTurnaroundTime,
        'RPC Entry Date': item.rpcEntryDate ? format(item.rpcEntryDate, 'dd/MMM/yyyy HH:mm:ss') : '',
        'RPC Exit Date': item.rpcExitDate ? format(item.rpcExitDate, 'dd/MMM/yyyy HH:mm:ss') : '',
        'Duration process time JJ:HH:MM:SS': item.durationProcessTimeFormatted,
        'Raw duration process time': item.rawDurationProcessTime,
        'Duration process time': item.durationProcessTime
      }));

      // Create worksheet
      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // Set column widths
      const columnWidths = [
        { wch: 15 }, // Serial Number
        { wch: 10 }, // Affiliate
        { wch: 20 }, // WorkItem No.
        { wch: 25 }, // Branch Name
        { wch: 15 }, // Current WorkStep
        { wch: 20 }, // Created By
        { wch: 25 }, // Workitem Creation Date
        { wch: 15 }, // Account No
        { wch: 25 }, // Account No Generation Date
        { wch: 15 }, // Zones
        { wch: 20 }, // Business Unit
        { wch: 15 }, // Risk Rating
        { wch: 25 }, // Account Authorization Date
        { wch: 15 }, // Account No Generation TAT(Min)
        { wch: 15 }, // CSR Submission TAT(Min)
        { wch: 20 }, // CSR Name
        { wch: 15 }, // CPC Maker Duration(Min)
        { wch: 20 }, // CPC Maker
        { wch: 15 }, // CPC Supervisor Duration (Min)
        { wch: 20 }, // CPC Supervisor
        { wch: 15 }, // Exception Resolution TAT(Min)
        { wch: 20 }, // CPC Exception Name
        { wch: 15 }, // Compliance Duration (Min)
        { wch: 20 }, // Compliance Name
        { wch: 15 }, // Total Turnaround Time(Min)
        { wch: 25 }, // RPC Entry Date
        { wch: 25 }, // RPC Exit Date
        { wch: 25 }, // Duration process time JJ:HH:MM:SS
        { wch: 15 }, // Raw duration process time
        { wch: 15 }  // Duration process time
      ];
      worksheet['!cols'] = columnWidths;

      // Create a new workbook
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'TAT Analysis');

      // Generate filename with current date
      const fileName = `TAT_Analysis_${format(new Date(), 'yyyy-MM-dd')}.xlsx`;

      // Save the file
      XLSX.writeFile(workbook, fileName);

      toast({
        title: "Export successful",
        description: `Data exported to ${fileName}`,
      });
    } catch (error: any) {
      console.error("Error exporting to Excel:", error);
      toast({
        title: "Export failed",
        description: `Error exporting data: ${error.message}`,
        variant: "destructive"
      });
    }
  };

  // Prepare data for the agent performance chart
  const getAgentPerformanceData = () => {
    const agentData: Record<string, Record<string, number>> = {};

    // Determine which agents to display
    const agentsToDisplay = showAgentSelector && selectedAgents.length > 0
      ? selectedAgents
      : selectedAgent !== "all" && !showAgentSelector
        ? [selectedAgent]
        : agents;

    // Initialize data structure
    agentsToDisplay.forEach(agent => {
      agentData[agent] = {
        '1 day': 0,
        '2 days': 0,
        '3+ days': 0
      };
    });

    // Count items by agent and duration
    filteredData.forEach(item => {
      if (!item.createdBy) return;

      // Skip if agent is not in the display list
      if (!agentsToDisplay.includes(item.createdBy)) return;

      const durationKey =
        item.durationProcessTime === 1 ? '1 day' :
        item.durationProcessTime === 2 ? '2 days' : '3+ days';

      if (agentData[item.createdBy]) {
        agentData[item.createdBy][durationKey]++;
      }
    });

    // Convert to chart format
    return Object.entries(agentData)
      .map(([agent, durations]) => ({
        agent,
        ...durations,
        total: Object.values(durations).reduce((sum, val) => sum + val, 0)
      }))
      .sort((a, b) => b.total - a.total); // Sort by total count descending
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">TAT Analysis</h1>
      <p className="text-gray-600 mb-8">
        Upload your TAT report to analyze turnaround times for account opening processes.
      </p>

      <div className="mb-8">
        <div className="flex flex-col items-center justify-center w-full max-w-md mx-auto">
          <input type="file" onChange={handleFileChange} className="mb-4" accept=".xlsx,.xls,.csv" />
          <div className="flex gap-4 w-full">
            <Button
              onClick={handleUpload}
              disabled={!file || isLoading}
              className="flex-1"
            >
              {isLoading ? 'Processing...' : 'Upload and Analyze'}
            </Button>
            <Button
              onClick={() => {
                // Load sample data
                const processedData = processExcelData(sampleTATData);
                setTatData(processedData);
                setFilteredData(processedData);

                // Extract unique agents
                const uniqueAgents = Array.from(new Set(processedData.map(item => item.createdBy)))
                  .filter(agent => agent) // Remove empty values
                  .sort();
                setAgents(uniqueAgents);
                setSelectedAgents(uniqueAgents); // Initialize with all agents selected

                // Find min and max dates
                const dates = processedData
                  .map(item => item.workitemCreationDate)
                  .filter(date => date !== null) as Date[];

                if (dates.length > 0) {
                  const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
                  const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));

                  // Set date range to min and max dates
                  setDateRange({
                    from: minDate,
                    to: maxDate
                  });
                }

                toast({
                  title: "Demo data loaded",
                  description: `Loaded ${processedData.length} sample TAT records.`
                });
              }}
              variant="outline"
              className="flex-1"
            >
              Load Demo Data
            </Button>
          </div>
        </div>
      </div>

      {filteredData.length > 0 && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Data Summary</CardTitle>
                <CardDescription>Overview of the loaded TAT data</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Total Records:</span>
                    <span>{tatData.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Filtered Records:</span>
                    <span>{filteredData.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Date Range:</span>
                    <span>
                      {dateRange?.from ? format(dateRange.from, "dd MMM yyyy") : "N/A"} - {dateRange?.to ? format(dateRange.to, "dd MMM yyyy") : "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Unique Agents:</span>
                    <span>{agents.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Selected Agents:</span>
                    <span>
                      {showAgentSelector
                        ? `${selectedAgents.length} of ${agents.length}`
                        : selectedAgent === "all" ? "All" : "1"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="font-medium">Average TAT:</span>
                    <span>
                      {filteredData.length > 0
                        ? `${(filteredData.reduce((sum, item) => sum + (item.totalTurnaroundTime || 0), 0) / filteredData.length).toFixed(1)} minutes`
                        : "N/A"}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Filters</CardTitle>
                <CardDescription>Filter the data by date range and agent</CardDescription>
              </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="space-y-2 flex-1">
                  <Label>Date Range</Label>
                  <div className="flex gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange?.from ? (
                            dateRange?.to ? (
                              <>
                                {format(dateRange.from, "PPP")} - {format(dateRange.to, "PPP")}
                              </>
                            ) : (
                              format(dateRange.from, "PPP")
                            )
                          ) : (
                            <span>Pick a date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          initialFocus
                          mode="range"
                          defaultMonth={dateRange?.from || new Date()}
                          selected={dateRange}
                          onSelect={setDateRange}
                          numberOfMonths={2}
                        />
                      </PopoverContent>
                    </Popover>

                    {(dateRange?.from || dateRange?.to) && (
                      <Button
                        variant="ghost"
                        onClick={() => setDateRange(undefined)}
                      >
                        Clear
                      </Button>
                    )}
                  </div>
                </div>

                <div className="space-y-2 flex-1">
                  <div className="flex justify-between items-center">
                    <Label>Agent (Created By)</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowAgentSelector(!showAgentSelector)}
                      className="h-8 px-2 text-xs"
                    >
                      {showAgentSelector ? 'Single Select' : 'Multi Select'}
                    </Button>
                  </div>

                  {!showAgentSelector ? (
                    <Select value={selectedAgent} onValueChange={setSelectedAgent}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an agent" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Agents</SelectItem>
                        {agents.map(agent => (
                          <SelectItem key={agent} value={agent}>{agent}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Card className="border">
                      <CardContent className="p-2">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">
                            Selected: {selectedAgents.length} of {agents.length}
                          </span>
                          <div className="space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={selectAllAgents}
                              className="h-7 px-2 text-xs"
                            >
                              Select All
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={deselectAllAgents}
                              className="h-7 px-2 text-xs"
                            >
                              Clear
                            </Button>
                          </div>
                        </div>
                        <ScrollArea className="h-40 rounded-md border">
                          <div className="p-2 space-y-1">
                            {agents.map(agent => (
                              <div key={agent} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`agent-${agent}`}
                                  checked={selectedAgents.includes(agent)}
                                  onCheckedChange={() => toggleAgentSelection(agent)}
                                />
                                <label
                                  htmlFor={`agent-${agent}`}
                                  className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  {agent}
                                </label>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                <span>TAT Data</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportToExcel}
                  className="flex items-center gap-1"
                >
                  <Download className="h-4 w-4" />
                  Export to Excel
                </Button>
              </CardTitle>
              <CardDescription>
                Showing {filteredData.length} of {tatData.length} records
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Work Item No.</TableHead>
                      <TableHead>Branch Name</TableHead>
                      <TableHead>Created By</TableHead>
                      <TableHead>Creation Date</TableHead>
                      <TableHead>Account No.</TableHead>
                      <TableHead>CSR Submission TAT</TableHead>
                      <TableHead>CPC Maker Duration</TableHead>
                      <TableHead>CPC Supervisor Duration</TableHead>
                      <TableHead>Exception Resolution TAT</TableHead>
                      <TableHead>Total TAT</TableHead>
                      <TableHead>Duration JJ:HH:MM:SS</TableHead>
                      <TableHead>Raw Duration</TableHead>
                      <TableHead>Duration Category</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredData.slice(0, 20).map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>{item.workItemNo}</TableCell>
                        <TableCell>{item.branchName}</TableCell>
                        <TableCell>{item.createdBy}</TableCell>
                        <TableCell>
                          {item.workitemCreationDate ?
                            format(item.workitemCreationDate, "PPP") :
                            "N/A"}
                        </TableCell>
                        <TableCell>{item.accountNo}</TableCell>
                        <TableCell>{item.csrSubmissionTAT || "N/A"}</TableCell>
                        <TableCell>{item.cpcMakerDuration || "N/A"}</TableCell>
                        <TableCell>{item.cpcSupervisorDuration || "N/A"}</TableCell>
                        <TableCell>{item.exceptionResolutionTAT || "N/A"}</TableCell>
                        <TableCell>{item.totalTurnaroundTime || "N/A"}</TableCell>
                        <TableCell>{item.durationProcessTimeFormatted || "N/A"}</TableCell>
                        <TableCell>{item.rawDurationProcessTime?.toFixed(2) || "N/A"}</TableCell>
                        <TableCell>
                          <div className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium w-fit",
                            item.durationProcessTime === 1 ? "bg-green-100 text-green-800" :
                            item.durationProcessTime === 2 ? "bg-yellow-100 text-yellow-800" :
                            "bg-red-100 text-red-800"
                          )}>
                            {item.durationProcessTime === 1 ? "1 day" :
                             item.durationProcessTime === 2 ? "2 days" : "3+ days"}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
