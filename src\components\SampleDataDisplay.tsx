"use client"

import { SurveyData } from '@/services/data-upload';

interface SampleDataDisplayProps {
  data: SurveyData[];
}

export function SampleDataDisplay({ data }: SampleDataDisplayProps) {
  return (
    <div className="rounded-md border overflow-hidden">
      <table className="w-full text-sm">
        <thead>
          <tr className="bg-muted/50">
            <th className="text-left font-medium p-2 border-b">ID</th>
            <th className="text-left font-medium p-2 border-b">Satisfaction</th>
            <th className="text-left font-medium p-2 border-b">Recommendation</th>
            <th className="text-left font-medium p-2 border-b">Feedback</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={index} className="hover:bg-muted/50 border-b">
              <td className="p-2">{item.ResponseID}</td>
              <td className="p-2">{item.Satisfaction}/5</td>
              <td className="p-2">{item.Recommendation}/10</td>
              <td className="p-2 truncate max-w-[200px]">{item.Feedback}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// Create a client-only version of the component to avoid hydration issues
export default function ClientSampleDataDisplay({ data }: SampleDataDisplayProps) {
  return <SampleDataDisplay data={data} />;
}
