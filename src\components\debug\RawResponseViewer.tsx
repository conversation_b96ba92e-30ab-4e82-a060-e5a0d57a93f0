"use client";

import { useState } from 'react';
import { <PERSON>, CardContent, CardD<PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, ChevronUp, Copy, Eye, EyeOff } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";

interface RawResponseViewerProps {
  responses: {
    type: string;
    prompt: string;
    response: string;
    model: string;
    timestamp: number;
  }[];
  visible: boolean;
}

export function RawResponseViewer({ responses, visible }: RawResponseViewerProps) {
  const [expanded, setExpanded] = useState<Record<number, boolean>>({});
  
  if (!visible || responses.length === 0) {
    return null;
  }

  const toggleExpanded = (index: number) => {
    setExpanded(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <Card className="mt-8 border-yellow-300 bg-yellow-50">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <Eye className="h-4 w-4 mr-2" />
          Debug: Raw AI Responses
        </CardTitle>
        <CardDescription>
          View the raw prompts and responses from the AI models
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="responses">
          <TabsList>
            <TabsTrigger value="responses">Responses ({responses.length})</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
          </TabsList>
          
          <TabsContent value="responses" className="space-y-4 mt-4">
            {responses.map((item, index) => (
              <Collapsible 
                key={index} 
                open={expanded[index]} 
                className="border rounded-md overflow-hidden"
              >
                <CollapsibleTrigger asChild>
                  <div 
                    className="flex justify-between items-center p-3 cursor-pointer hover:bg-yellow-100"
                    onClick={() => toggleExpanded(index)}
                  >
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono text-xs">
                        {item.type}
                      </Badge>
                      <span className="text-sm font-medium">
                        {item.model}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {new Date(item.timestamp).toLocaleTimeString()}
                      </Badge>
                    </div>
                    <div className="flex items-center">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          copyToClipboard(item.response);
                        }}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      {expanded[index] ? 
                        <ChevronUp className="h-4 w-4" /> : 
                        <ChevronDown className="h-4 w-4" />
                      }
                    </div>
                  </div>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <div className="border-t p-3 space-y-3">
                    <div>
                      <h4 className="text-xs font-semibold mb-1">Prompt:</h4>
                      <ScrollArea className="h-32 rounded border bg-slate-50 p-2">
                        <pre className="text-xs font-mono whitespace-pre-wrap">{item.prompt}</pre>
                      </ScrollArea>
                    </div>
                    <div>
                      <h4 className="text-xs font-semibold mb-1">Response:</h4>
                      <ScrollArea className="h-64 rounded border bg-slate-50 p-2">
                        <pre className="text-xs font-mono whitespace-pre-wrap">{item.response}</pre>
                      </ScrollArea>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            ))}
          </TabsContent>
          
          <TabsContent value="timeline" className="mt-4">
            <div className="relative border-l-2 border-yellow-300 pl-4 ml-2 space-y-4">
              {responses.map((item, index) => (
                <div key={index} className="relative">
                  <div className="absolute -left-6 top-1 w-4 h-4 rounded-full bg-yellow-300"></div>
                  <div className="mb-1">
                    <Badge variant="outline" className="font-mono text-xs mr-2">
                      {item.type}
                    </Badge>
                    <span className="text-sm font-medium">
                      {item.model}
                    </span>
                    <span className="text-xs text-gray-500 ml-2">
                      {new Date(item.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 mb-1">
                    Prompt length: {item.prompt.length} chars
                  </div>
                  <div className="text-xs text-gray-600">
                    Response length: {item.response.length} chars
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="pt-0">
        <div className="text-xs text-gray-500">
          This debug view shows the raw AI interactions. Use it to troubleshoot issues with AI responses.
        </div>
      </CardFooter>
    </Card>
  );
}
