# **App Name**: Survey Insights Analyzer

## Core Features:

- Data Upload: Upload survey data from a CSV or Excel file.
- KPI Detection: Analyze numerical survey responses to identify the most impactful KPIs using correlation analysis.
- Thematic Analysis: Categorize open-ended survey responses (verbatim) by themes using AI-powered natural language processing.
- Sentiment Analysis: Use an AI model to analyze the sentiment (feeling or satisfaction) expressed in the verbatim responses. The AI model is used as a tool, allowing it to choose whether to incorporate certain pieces of information from the survey.
- Dashboard Visualization: Display the identified KPIs, thematic categories, and sentiment scores in an easy-to-understand dashboard.

## Style Guidelines:

- Primary color: Neutral white or light gray for a clean look.
- Secondary color: A calming blue (#3498db) to represent insights.
- Accent: Teal (#008080) for interactive elements and key highlights.
- Clean and readable sans-serif font for data clarity.
- Use simple and intuitive icons to represent different KPIs and categories.
- Well-structured dashboard layout with clear sections for KPIs, thematic analysis, and sentiment analysis.

## Original User Request:
an to analyse survey data and detect the most impactful kpi and categorise verbatum by thematics or feeling or satisfaction
  