import { NextResponse } from 'next/server';
import axios from 'axios';

const OLLAMA_BASE_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

// Fallback URLs for WSL environments
const FALLBACK_URLS = [
  'http://localhost:11434',
  'http://************:11434',
  'http://host.docker.internal:11434'
];

// Helper function to try connecting to Ollama with fallback URLs
async function tryOllamaConnection() {
  const urlsToTry = [OLLAMA_BASE_URL, ...FALLBACK_URLS.filter(url => url !== OLLAMA_BASE_URL)];

  for (const url of urlsToTry) {
    try {
      console.log(`Trying Ollama connection at: ${url}`);
      const response = await axios.get(`${url}/api/tags`, {
        timeout: 3000 // 3 second timeout
      });
      console.log(`Successfully connected to Ollama at: ${url}`);
      return { response, url };
    } catch (error: any) {
      console.log(`Failed to connect to Ollama at ${url}: ${error.message}`);
      continue;
    }
  }
  throw new Error('Failed to connect to Ollama on any URL');
}

export async function GET() {
  try {
    // Try to connect to Ollama API with fallback URLs
    const { response, url } = await tryOllamaConnection();

    // Extract model information
    const models = response.data.models || [];
    const modelNames = models.map((model: any) => model.name);

    // Check if we have any of our configured models available
    const requiredModels = ['gemma3', 'qwen2.5-coder', 'deepseek-r1:14b', 'mistral'];
    const availableRequiredModels = requiredModels.filter(model => modelNames.includes(model));
    const missingModels = requiredModels.filter(model => !modelNames.includes(model));

    // If we get here, Ollama is running
    return NextResponse.json({
      status: 'online',
      models: modelNames,
      availableRequiredModels,
      missingModels,
      defaultModel: availableRequiredModels.length > 0 ? availableRequiredModels[0] : null,
      installCommand: missingModels.length > 0 ?
        `ollama pull ${missingModels[0]}` :
        null,
      connectedUrl: url // Include which URL worked
    });
  } catch (error: any) {
    // Check for specific error types
    if (error.message.includes('ECONNREFUSED') || error.message.includes('Failed to connect')) {
      return NextResponse.json({
        status: 'offline',
        error: 'Failed to connect to Ollama. Make sure it is running and accessible.',
        installInstructions: 'For WSL: Run "OLLAMA_HOST=0.0.0.0:11434 ollama serve" in WSL terminal.',
        triedUrls: [OLLAMA_BASE_URL, ...FALLBACK_URLS]
      }, { status: 503 }); // Service Unavailable
    }

    console.error('Ollama connection error:', error.message);
    return NextResponse.json({
      status: 'offline',
      error: `Failed to connect to Ollama: ${error.message}`,
      details: error.response?.data || null
    }, { status: 503 }); // Service Unavailable
  }
}
