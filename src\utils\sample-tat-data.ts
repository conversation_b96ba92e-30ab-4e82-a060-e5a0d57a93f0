/**
 * Generates sample TAT (Turnaround Time) data for testing the TAT Analysis page.
 * This simulates an Excel file with account opening process data.
 */

// Define the structure of a TAT report item
export interface SampleTATItem {
  'numéro de série': string;
  'AFFILIATE': string;
  'WorkItem No.': string;
  'Branch Name': string;
  'Current WorkStep': string;
  'Created By': string;
  'Workitem_Creation_Date': string;
  'ACCOUNT_NO': string;
  'Account No Generation Date': string | null;
  'Zones': string;
  'Business Unit': string;
  'Risk Rating': string;
  'Account_Authorization_Date': string | null;
  'Account No Generation TAT(Min)': number | null;
  'CSR Submission TAT(Min)': number | null;
  'CSR Name': string;
  'CPC Maker Duration(Min)': number | null;
  'CPC Maker': string;
  'CPC Supervisor Duration (Min)': number | null;
  'CPC Supervisor': string;
  'Exception Resolution TAT(Min)': number | null;
  'CPC Exception Name': string;
  'Compliance Duration (Min)': number | null;
  'Compliance Name': string;
  'Total Turnaround Time(Min)': number | null;
  'RPC Entry Date': string | null;
  'RPC Exit Date': string | null;
  'Duration process time JJ:HH:MM:SS': string;
  'Raw duration process time': number;
  'Duration process time': number;
}

// Sample branch names
const branchNames = [
  'Main Branch',
  'Downtown Branch',
  'Westside Branch',
  'Eastside Branch',
  'North Branch',
  'South Branch',
  'Central Branch',
  'Corporate Branch',
  'University Branch',
  'Industrial Branch'
];

// Sample agents (Created By)
const agents = [
  'John Smith',
  'Mary Johnson',
  'Robert Williams',
  'Sarah Brown',
  'Michael Davis',
  'Jennifer Miller',
  'David Wilson',
  'Lisa Moore',
  'James Taylor',
  'Patricia Anderson'
];

// Sample CSR names
const csrNames = [
  'Emma Thompson',
  'Daniel Martinez',
  'Olivia Garcia',
  'William Rodriguez',
  'Sophia Lee',
  'Alexander Wright',
  'Isabella Lopez',
  'Ethan Hill',
  'Mia Scott',
  'Benjamin Green'
];

// Sample CPC Makers
const cpcMakers = [
  'Ava Allen',
  'Noah King',
  'Charlotte Baker',
  'Jacob Nelson',
  'Amelia Carter'
];

// Sample CPC Supervisors
const cpcSupervisors = [
  'Liam Adams',
  'Harper Mitchell',
  'Lucas Roberts',
  'Ella Phillips',
  'Mason Campbell'
];

// Sample zones
const zones = [
  'North Zone',
  'South Zone',
  'East Zone',
  'West Zone',
  'Central Zone'
];

// Sample business units
const businessUnits = [
  'Retail Banking',
  'Corporate Banking',
  'Private Banking',
  'SME Banking',
  'Institutional Banking'
];

// Sample risk ratings
const riskRatings = [
  'Low',
  'Medium',
  'High'
];

// Generate a random date within a range
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Format date to ISO string (YYYY-MM-DD)
function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

// Generate a random number within a range
function randomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Pick a random item from an array
function randomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// Format duration in JJ:HH:MM:SS format
function formatDuration(minutes: number): string {
  const days = Math.floor(minutes / (24 * 60));
  const hours = Math.floor((minutes % (24 * 60)) / 60);
  const mins = Math.floor(minutes % 60);
  const secs = Math.floor((minutes * 60) % 60);
  
  return `${days.toString().padStart(2, '0')}:${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Generate sample TAT data
export function generateSampleTATData(count: number = 100): SampleTATItem[] {
  const data: SampleTATItem[] = [];
  const endDate = new Date();
  const startDate = new Date();
  startDate.setMonth(startDate.getMonth() - 3); // 3 months ago
  
  for (let i = 0; i < count; i++) {
    // Generate creation date
    const creationDate = randomDate(startDate, endDate);
    
    // Generate random durations
    const csrSubmissionTAT = randomNumber(10, 120);
    const cpcMakerDuration = randomNumber(30, 240);
    const cpcSupervisorDuration = randomNumber(20, 180);
    const exceptionResolutionTAT = Math.random() > 0.7 ? randomNumber(60, 480) : null; // 30% chance of exception
    const complianceDuration = Math.random() > 0.8 ? randomNumber(60, 360) : null; // 20% chance of compliance review
    
    // Calculate total TAT
    let totalTAT = csrSubmissionTAT + cpcMakerDuration + cpcSupervisorDuration;
    if (exceptionResolutionTAT) totalTAT += exceptionResolutionTAT;
    if (complianceDuration) totalTAT += complianceDuration;
    
    // Calculate exit date
    const exitDate = new Date(creationDate.getTime() + totalTAT * 60 * 1000);
    
    // Calculate raw duration in days
    const rawDuration = (exitDate.getTime() - creationDate.getTime()) / (24 * 60 * 60 * 1000);
    
    // Determine duration process time category (1, 2, or 3 days)
    let durationProcessTime = 3; // Default to 3+ days
    if (rawDuration < 1) {
      durationProcessTime = 1;
    } else if (rawDuration >= 1 && rawDuration < 2) {
      durationProcessTime = 2;
    }
    
    // Generate account number generation date (80% chance of having one)
    const accountNoGenerationDate = Math.random() > 0.2 
      ? new Date(creationDate.getTime() + randomNumber(30, 120) * 60 * 1000) 
      : null;
    
    // Generate account authorization date (70% chance of having one)
    const accountAuthorizationDate = Math.random() > 0.3 
      ? new Date(creationDate.getTime() + randomNumber(60, 240) * 60 * 1000) 
      : null;
    
    // Generate RPC entry date (90% chance of having one)
    const rpcEntryDate = Math.random() > 0.1 
      ? new Date(creationDate.getTime() + randomNumber(totalTAT - 60, totalTAT - 10) * 60 * 1000) 
      : null;
    
    data.push({
      'numéro de série': `SR${(i + 1).toString().padStart(5, '0')}`,
      'AFFILIATE': `AFF${randomNumber(100, 999)}`,
      'WorkItem No.': `WI${randomNumber(10000, 99999)}`,
      'Branch Name': randomItem(branchNames),
      'Current WorkStep': 'Completed',
      'Created By': randomItem(agents),
      'Workitem_Creation_Date': formatDate(creationDate),
      'ACCOUNT_NO': `AC${randomNumber(100000, 999999)}`,
      'Account No Generation Date': accountNoGenerationDate ? formatDate(accountNoGenerationDate) : null,
      'Zones': randomItem(zones),
      'Business Unit': randomItem(businessUnits),
      'Risk Rating': randomItem(riskRatings),
      'Account_Authorization_Date': accountAuthorizationDate ? formatDate(accountAuthorizationDate) : null,
      'Account No Generation TAT(Min)': accountNoGenerationDate ? randomNumber(20, 100) : null,
      'CSR Submission TAT(Min)': csrSubmissionTAT,
      'CSR Name': randomItem(csrNames),
      'CPC Maker Duration(Min)': cpcMakerDuration,
      'CPC Maker': randomItem(cpcMakers),
      'CPC Supervisor Duration (Min)': cpcSupervisorDuration,
      'CPC Supervisor': randomItem(cpcSupervisors),
      'Exception Resolution TAT(Min)': exceptionResolutionTAT,
      'CPC Exception Name': exceptionResolutionTAT ? `Exception ${randomNumber(1, 5)}` : '',
      'Compliance Duration (Min)': complianceDuration,
      'Compliance Name': complianceDuration ? `Compliance Officer ${randomNumber(1, 3)}` : '',
      'Total Turnaround Time(Min)': totalTAT,
      'RPC Entry Date': rpcEntryDate ? formatDate(rpcEntryDate) : null,
      'RPC Exit Date': formatDate(exitDate),
      'Duration process time JJ:HH:MM:SS': formatDuration(totalTAT),
      'Raw duration process time': rawDuration,
      'Duration process time': durationProcessTime
    });
  }
  
  return data;
}

// Export a pre-generated sample for quick testing
export const sampleTATData = generateSampleTATData(50);
